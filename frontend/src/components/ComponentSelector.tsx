import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from './ui/tabs';
import AddLinkForm from './AddLinkForm';
import AddAnnouncementForm from './AddAnnouncementForm';
import AddShuffleCardsForm from './AddShuffleCardsForm';
import AddMiniLeadForm from './AddMiniLeadForm';
import AddVideoForm from './AddVideoForm';
import AddEmbeddedForm from './AddEmbeddedForm';
import AddPaymentForm from './AddPaymentForm';
import AddDigitalProductForm from './AddDigitalProductForm';
import AddTipJarForm from './AddTipJarForm';
import { ShuffleCard, MiniLeadField, VisibilityDates } from '../api/links';

interface ComponentSelectorProps {
  onAddLink: (title: string, url: string, icon?: string) => void;
  onAddAnnouncement: (
    title: string,
    text: string,
    backgroundColor: string,
    textColor: string,
    url?: string
  ) => void;
  onAddShuffleCards: (title: string, shuffleCards: ShuffleCard[]) => void;
  onAddMiniLead: (title: string, introText: string, fields: MiniLeadField[]) => void;
  onAddVideo: (
    title: string,
    videoUrl: string,
    thumbnailUrl: string,
    orientation: 'portrait' | 'landscape',
    duration: number
  ) => void;
  onAddEmbedded: (
    title: string,
    embedType: 'youtube' | 'tiktok',
    autoplay: boolean,
    muted: boolean,
    showControls: boolean,
    youtubeCode?: string,
    tikTokUrl?: string,
    password?: string,
    passwordEnabled?: boolean,
    passwordExpiryDate?: string,
    visibilityDates?: VisibilityDates
  ) => void;
  onAddPayment?: (title: string, description: string) => void;
  onAddDigitalProduct?: (
    title: string,
    description: string,
    productType: 'template' | 'preset' | 'guide' | 'ebook' | 'course' | 'software' | 'other',
    price: number,
    currency: string,
    downloadUrl: string,
    previewImages: string[],
    fileSize: string,
    fileFormat: string
  ) => void;
  onAddTipJar?: (
    title: string,
    tipMessage: string,
    thankYouMessage: string,
    tipAmounts: number[],
    customAmountEnabled: boolean,
    minTipAmount: number,
    maxTipAmount: number
  ) => void;
}

const ComponentSelector = ({
  onAddLink,
  onAddAnnouncement,
  onAddShuffleCards,
  onAddMiniLead,
  onAddVideo,
  onAddEmbedded,
  onAddPayment,
  onAddDigitalProduct,
  onAddTipJar
}: ComponentSelectorProps) => {
  const [activeTab, setActiveTab] = useState('link');

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle>Add New Component</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="link" value={activeTab} onValueChange={setActiveTab}>
          <div className="mb-4 space-y-2">
            <TabsList className="grid grid-cols-5 w-full">
              <TabsTrigger value="link">Link</TabsTrigger>
              <TabsTrigger value="announcement">Post</TabsTrigger>
              <TabsTrigger value="shuffle">Card</TabsTrigger>
              <TabsTrigger value="minilead">Mini Lead</TabsTrigger>
              <TabsTrigger value="video">Video</TabsTrigger>
            </TabsList>
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger value="embedded">Embedded</TabsTrigger>
              <TabsTrigger value="payment">Payment</TabsTrigger>
              <TabsTrigger value="digitalproduct">Digital Product</TabsTrigger>
              <TabsTrigger value="tipjar">Tip Jar</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="link">
            <AddLinkForm onAdd={onAddLink} />
          </TabsContent>

          <TabsContent value="announcement">
            <AddAnnouncementForm onAdd={onAddAnnouncement} />
          </TabsContent>

          <TabsContent value="shuffle">
            <AddShuffleCardsForm onAdd={onAddShuffleCards} />
          </TabsContent>

          <TabsContent value="minilead">
            <AddMiniLeadForm onAdd={onAddMiniLead} />
          </TabsContent>

          <TabsContent value="video">
            <AddVideoForm onAdd={onAddVideo} />
          </TabsContent>

          <TabsContent value="embedded">
            <AddEmbeddedForm onAdd={onAddEmbedded} />
          </TabsContent>

          <TabsContent value="payment">
            {onAddPayment && <AddPaymentForm onAdd={onAddPayment} />}
          </TabsContent>

          <TabsContent value="digitalproduct">
            {onAddDigitalProduct && <AddDigitalProductForm onAdd={onAddDigitalProduct} />}
          </TabsContent>

          <TabsContent value="tipjar">
            {onAddTipJar && <AddTipJarForm onAdd={onAddTipJar} />}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ComponentSelector;
