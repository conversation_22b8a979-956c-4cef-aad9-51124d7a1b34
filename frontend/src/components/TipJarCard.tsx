import { useState } from 'react';
import { <PERSON> } from '../api/links';
import { <PERSON>, CardContent, CardFooter } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Switch } from './ui/switch';
import { Badge } from './ui/badge';
import RichTextEditor from './RichTextEditor';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog';
import { Pencil, Archive, ArchiveRestore, Trash2, GripVertical, Heart, X } from 'lucide-react';

interface TipJarCardProps {
  link: Link;
  onEdit: (link: Link) => void;
  onDelete: (id: string) => void;
  onArchive?: (id: string) => void;
  onUnarchive?: (id: string) => void;
  onToggleEnabled: (id: string, enabled: boolean) => void;
  isDraggable?: boolean;
  dragHandleProps?: any;
}

const TipJarCard = ({
  link,
  onEdit,
  onDelete,
  onArchive,
  onUnarchive,
  onToggleEnabled,
  isDraggable = false,
  dragHandleProps = {}
}: TipJarCardProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [title, setTitle] = useState(link.title);
  const [tipMessage, setTipMessage] = useState(link.tipMessage || '');
  const [thankYouMessage, setThankYouMessage] = useState(link.thankYouMessage || '');
  const [tipAmounts, setTipAmounts] = useState<number[]>(link.tipAmounts || [5, 10, 25]);
  const [customAmountEnabled, setCustomAmountEnabled] = useState(link.customAmountEnabled ?? true);
  const [minTipAmount, setMinTipAmount] = useState(link.minTipAmount?.toString() || '1');
  const [maxTipAmount, setMaxTipAmount] = useState(link.maxTipAmount?.toString() || '1000');
  const [enabled, setEnabled] = useState(link.enabled);
  const [newTipAmount, setNewTipAmount] = useState('');

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    onEdit({
      ...link,
      title,
      tipMessage,
      thankYouMessage,
      tipAmounts,
      customAmountEnabled,
      minTipAmount: parseFloat(minTipAmount),
      maxTipAmount: parseFloat(maxTipAmount),
      enabled
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTitle(link.title);
    setTipMessage(link.tipMessage || '');
    setThankYouMessage(link.thankYouMessage || '');
    setTipAmounts(link.tipAmounts || [5, 10, 25]);
    setCustomAmountEnabled(link.customAmountEnabled ?? true);
    setMinTipAmount(link.minTipAmount?.toString() || '1');
    setMaxTipAmount(link.maxTipAmount?.toString() || '1000');
    setEnabled(link.enabled);
    setNewTipAmount('');
    setIsEditing(false);
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    onDelete(link._id);
    setIsDeleteDialogOpen(false);
  };

  const handleArchive = () => {
    if (onArchive) {
      onArchive(link._id);
    }
  };

  const handleUnarchive = () => {
    if (onUnarchive) {
      onUnarchive(link._id);
    }
  };

  const handleToggleEnabled = () => {
    const newEnabledState = !enabled;
    setEnabled(newEnabledState);
    onToggleEnabled(link._id, newEnabledState);
  };

  const addTipAmount = () => {
    const amount = parseFloat(newTipAmount);
    if (amount > 0 && !tipAmounts.includes(amount) && tipAmounts.length < 6) {
      setTipAmounts([...tipAmounts, amount].sort((a, b) => a - b));
      setNewTipAmount('');
    }
  };

  const removeTipAmount = (amount: number) => {
    setTipAmounts(tipAmounts.filter(a => a !== amount));
  };

  if (isEditing) {
    return (
      <Card className="mb-4 border-l-4 border-l-pink-500">
        <CardContent className="p-4">
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-title">Tip Jar Title</Label>
              <Input
                id="edit-title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="edit-tip-message">Tip Message</Label>
              <div className="mt-1">
                <RichTextEditor
                  value={tipMessage}
                  onChange={setTipMessage}
                  placeholder="Support my work with a tip!"
                  className="min-h-[80px]"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="edit-thank-you-message">Thank You Message</Label>
              <div className="mt-1">
                <RichTextEditor
                  value={thankYouMessage}
                  onChange={setThankYouMessage}
                  placeholder="Thank you for your generous tip!"
                  className="min-h-[80px]"
                />
              </div>
            </div>

            <div>
              <Label>Preset Tip Amounts</Label>
              <div className="mt-2 space-y-2">
                <div className="flex flex-wrap gap-2">
                  {tipAmounts.map((amount) => (
                    <Badge key={amount} variant="secondary" className="flex items-center gap-1">
                      ${amount}
                      <button
                        type="button"
                        onClick={() => removeTipAmount(amount)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>

                {tipAmounts.length < 6 && (
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      placeholder="Add amount"
                      value={newTipAmount}
                      onChange={(e) => setNewTipAmount(e.target.value)}
                      className="w-32"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addTipAmount}
                      disabled={!newTipAmount || tipAmounts.length >= 6}
                    >
                      Add
                    </Button>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="custom-amount"
                checked={customAmountEnabled}
                onCheckedChange={setCustomAmountEnabled}
              />
              <Label htmlFor="custom-amount">Allow custom tip amounts</Label>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-min-tip">Minimum Tip Amount</Label>
                <Input
                  id="edit-min-tip"
                  type="number"
                  step="0.01"
                  min="0.01"
                  value={minTipAmount}
                  onChange={(e) => setMinTipAmount(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="edit-max-tip">Maximum Tip Amount</Label>
                <Input
                  id="edit-max-tip"
                  type="number"
                  step="0.01"
                  min="1"
                  value={maxTipAmount}
                  onChange={(e) => setMaxTipAmount(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <>
      <Card className="mb-4 border-l-4 border-l-pink-500">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1">
              {isDraggable && (
                <div {...dragHandleProps} className="cursor-grab">
                  <GripVertical className="h-5 w-5 text-muted-foreground" />
                </div>
              )}
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center">
                  <Heart className="h-5 w-5 text-pink-600" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold truncate">{link.title}</h3>
                <p className="text-sm text-muted-foreground truncate">
                  Tip amounts: {tipAmounts.map(amount => `$${amount}`).join(', ')}
                  {customAmountEnabled && ' + custom'}
                </p>
                {link.tipMessage && (
                  <div
                    className="text-sm text-muted-foreground mt-1 line-clamp-2"
                    dangerouslySetInnerHTML={{ __html: link.tipMessage }}
                  />
                )}
              </div>
            </div>
            <div className="flex items-center gap-2 ml-4">
              <Switch
                checked={enabled}
                onCheckedChange={handleToggleEnabled}
                aria-label="Toggle visibility"
              />
              <Button variant="ghost" size="icon" onClick={handleEdit}>
                <Pencil className="h-4 w-4" />
              </Button>
              {onArchive && (
                <Button variant="ghost" size="icon" onClick={handleArchive}>
                  <Archive className="h-4 w-4" />
                </Button>
              )}
              {onUnarchive && (
                <Button variant="ghost" size="icon" onClick={handleUnarchive}>
                  <ArchiveRestore className="h-4 w-4" />
                </Button>
              )}
              <Button variant="ghost" size="icon" onClick={handleDelete}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tip Jar</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this tip jar? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default TipJarCard;
