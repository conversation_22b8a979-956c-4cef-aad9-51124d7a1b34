import { useState } from 'react';
import { <PERSON> } from '../api/links';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';

import { Heart, Coffee, Gift, Star } from 'lucide-react';

interface TipJarWidgetProps {
  link: Link;
  isPreview?: boolean;
}

const TipJarWidget = ({ link, isPreview = false }: TipJarWidgetProps) => {
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [customAmount, setCustomAmount] = useState('');
  const [tipperName, setTipperName] = useState('');
  const [tipperMessage, setTipperMessage] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [selectedGift, setSelectedGift] = useState('heart');
  const [isLoading, setIsLoading] = useState(false);
  const [showThankYou, setShowThankYou] = useState(false);
  const [isExpanded, setIsExpanded] = useState(isPreview); // Expand by default in preview mode

  const giftOptions = [
    { type: 'heart', emoji: '❤️', icon: Heart, label: 'Heart' },
    { type: 'coffee', emoji: '☕', icon: Coffee, label: 'Coffee' },
    { type: 'gift', emoji: '🎁', icon: Gift, label: 'Gift' },
    { type: 'star', emoji: '⭐', icon: Star, label: 'Star' },
  ];

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    setSelectedAmount(null);
  };

  const getCurrentAmount = () => {
    if (selectedAmount) return selectedAmount;
    if (customAmount) return parseFloat(customAmount);
    return 0;
  };

  const handleSendTip = async () => {
    if (isPreview) {
      alert('This is a preview. Tip functionality is not available.');
      return;
    }

    const amount = getCurrentAmount();
    if (!amount || amount < (link.minTipAmount || 1)) {
      alert(`Minimum tip amount is $${link.minTipAmount || 1}`);
      return;
    }

    if (amount > (link.maxTipAmount || 1000)) {
      alert(`Maximum tip amount is $${link.maxTipAmount || 1000}`);
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement tip sending flow
      // This would typically:
      // 1. Create a tip transaction
      // 2. Process payment
      // 3. Show success message
      console.log('Sending tip:', {
        amount,
        tipperName: isAnonymous ? undefined : tipperName,
        tipperMessage,
        giftType: selectedGift
      });

      // Mock tip flow
      setTimeout(() => {
        setShowThankYou(true);
        setIsLoading(false);

        // Reset form after showing thank you
        setTimeout(() => {
          setShowThankYou(false);
          setSelectedAmount(null);
          setCustomAmount('');
          setTipperName('');
          setTipperMessage('');
        }, 3000);
      }, 1000);
    } catch (error) {
      console.error('Tip failed:', error);
      setIsLoading(false);
    }
  };

  if (showThankYou) {
    return (
      <Card className="w-full max-w-md mx-auto text-center">
        <CardContent className="p-8">
          <div className="text-6xl mb-4">
            {giftOptions.find(g => g.type === selectedGift)?.emoji || '❤️'}
          </div>
          <h3 className="text-xl font-semibold mb-2">Thank You!</h3>
          <div
            className="text-muted-foreground"
            dangerouslySetInnerHTML={{
              __html: link.thankYouMessage || 'Thank you for your generous tip!'
            }}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader
        className={`text-center ${!isExpanded ? 'cursor-pointer hover:bg-gray-50' : ''}`}
        onClick={() => !isExpanded && setIsExpanded(true)}
      >
        <CardTitle className="flex items-center justify-center gap-2">
          <Heart className="h-5 w-5 text-pink-500" />
          {link.title}
        </CardTitle>
        {isExpanded && link.tipMessage && (
          <div
            className="text-sm text-muted-foreground"
            dangerouslySetInnerHTML={{ __html: link.tipMessage }}
          />
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
        {/* Preset Amounts */}
        {link.tipAmounts && link.tipAmounts.length > 0 && (
          <div>
            <Label className="text-sm font-medium">Choose an amount</Label>
            <div className="grid grid-cols-3 gap-2 mt-2">
              {link.tipAmounts.map((amount) => (
                <Button
                  key={amount}
                  variant={selectedAmount === amount ? "default" : "outline"}
                  onClick={() => handleAmountSelect(amount)}
                  className="h-12"
                >
                  ${amount}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Custom Amount */}
        {link.customAmountEnabled && (
          <div>
            <Label htmlFor="custom-amount" className="text-sm font-medium">
              Or enter custom amount
            </Label>
            <Input
              id="custom-amount"
              type="number"
              step="0.01"
              min={link.minTipAmount || 1}
              max={link.maxTipAmount || 1000}
              placeholder={`$${link.minTipAmount || 1} - $${link.maxTipAmount || 1000}`}
              value={customAmount}
              onChange={(e) => handleCustomAmountChange(e.target.value)}
              className="mt-1"
            />
          </div>
        )}

        {/* Gift Type Selection */}
        <div>
          <Label className="text-sm font-medium">Choose a gift</Label>
          <div className="grid grid-cols-4 gap-2 mt-2">
            {giftOptions.map((gift) => {
              const IconComponent = gift.icon;
              return (
                <Button
                  key={gift.type}
                  variant={selectedGift === gift.type ? "default" : "outline"}
                  onClick={() => setSelectedGift(gift.type)}
                  className="h-12 flex flex-col items-center gap-1"
                >
                  <IconComponent className="h-4 w-4" />
                  <span className="text-xs">{gift.label}</span>
                </Button>
              );
            })}
          </div>
        </div>

        {/* Tipper Information */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="anonymous"
              checked={isAnonymous}
              onChange={(e) => setIsAnonymous(e.target.checked)}
              className="rounded"
            />
            <Label htmlFor="anonymous" className="text-sm">
              Send anonymously
            </Label>
          </div>

          {!isAnonymous && (
            <div>
              <Label htmlFor="tipper-name" className="text-sm font-medium">
                Your name (optional)
              </Label>
              <Input
                id="tipper-name"
                placeholder="Enter your name"
                value={tipperName}
                onChange={(e) => setTipperName(e.target.value)}
                className="mt-1"
              />
            </div>
          )}

          <div>
            <Label htmlFor="tipper-message" className="text-sm font-medium">
              Message (optional)
            </Label>
            <Textarea
              id="tipper-message"
              placeholder="Leave a nice message..."
              value={tipperMessage}
              onChange={(e) => setTipperMessage(e.target.value)}
              className="mt-1"
              rows={2}
              maxLength={500}
            />
          </div>
        </div>

        {/* Send Tip Button */}
        <Button
          onClick={handleSendTip}
          disabled={isLoading || getCurrentAmount() === 0}
          className="w-full"
          size="lg"
        >
          <Heart className="h-4 w-4 mr-2" />
          {isLoading
            ? 'Processing...'
            : `Send Tip ${getCurrentAmount() > 0 ? `- $${getCurrentAmount().toFixed(2)}` : ''}`
          }
        </Button>

        {/* Minimum/Maximum Info */}
        <div className="text-xs text-muted-foreground text-center">
          <p>
            Tip range: ${link.minTipAmount || 1} - ${link.maxTipAmount || 1000}
          </p>
        </div>
        </CardContent>
      )}
    </Card>
  );
};

export default TipJarWidget;
