import { SESClient, Send<PERSON>mailCommand, Send<PERSON>mailCommandInput, SendEmailCommandOutput } from '@aws-sdk/client-ses';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import Handlebars from 'handlebars';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Configure AWS SDK v3
const sesClient = new SESClient({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
});

// Register Handlebars helpers
Handlebars.registerHelper('eq', function(a, b) {
  return a === b;
});

// Load email templates
const templateDir = path.join(__dirname, '../templates/emails');

// Ensure templates directory exists
if (!fs.existsSync(templateDir)) {
  fs.mkdirSync(templateDir, { recursive: true });
}

// Template paths
const invitationTemplatePath = path.join(templateDir, 'invitation.html');
const confirmationTemplatePath = path.join(templateDir, 'confirmation.html');

// Default templates if files don't exist
const defaultInvitationTemplate = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invitation to Collaborate</title>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      padding: 20px 0;
    }
    .logo {
      max-width: 150px;
    }
    .content {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 30px;
      margin-bottom: 20px;
    }
    .button {
      display: inline-block;
      background-color: #3b82f6;
      color: white;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 4px;
      font-weight: 500;
      margin: 20px 0;
    }
    .footer {
      text-align: center;
      font-size: 12px;
      color: #666;
      padding: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://hithere.com/logo.png" alt="HiThere Logo" class="logo">
    </div>
    <div class="content">
      <h1>You've been invited to collaborate!</h1>
      <p>Hello,</p>
      <p><strong>{{inviterName}}</strong> has invited you to collaborate on their profile as a <strong>{{role}}</strong>.</p>
      <p>As a {{role}}, you'll be able to:</p>
      <ul>
        {{#if (eq role "editor")}}
          <li>Edit profile content and components</li>
          <li>Manage profile appearance</li>
          <li>View profile statistics</li>
        {{else}}
          <li>Full access to all profile features</li>
          <li>Manage collaborators and permissions</li>
          <li>Change profile URL and settings</li>
        {{/if}}
      </ul>
      <p>Click the button below to accept this invitation:</p>
      <a href="{{invitationUrl}}" class="button">Accept Invitation</a>
      <p>This invitation will expire in 7 days.</p>
    </div>
    <div class="footer">
      <p>If you didn't expect this invitation, you can safely ignore this email.</p>
      <p>&copy; 2023 HiThere. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
`;

const defaultConfirmationTemplate = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Collaboration Confirmed</title>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      padding: 20px 0;
    }
    .logo {
      max-width: 150px;
    }
    .content {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 30px;
      margin-bottom: 20px;
    }
    .button {
      display: inline-block;
      background-color: #3b82f6;
      color: white;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 4px;
      font-weight: 500;
      margin: 20px 0;
    }
    .footer {
      text-align: center;
      font-size: 12px;
      color: #666;
      padding: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://hithere.com/logo.png" alt="HiThere Logo" class="logo">
    </div>
    <div class="content">
      <h1>You're now a collaborator!</h1>
      <p>Hello,</p>
      <p>You have successfully accepted the invitation to collaborate on <strong>{{profileName}}</strong>'s profile as a <strong>{{role}}</strong>.</p>
      <p>You can now access the profile and start collaborating:</p>
      <a href="{{profileUrl}}" class="button">Go to Dashboard</a>
    </div>
    <div class="footer">
      <p>&copy; 2023 HiThere. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
`;

// Write default templates if they don't exist
if (!fs.existsSync(invitationTemplatePath)) {
  fs.writeFileSync(invitationTemplatePath, defaultInvitationTemplate);
}

if (!fs.existsSync(confirmationTemplatePath)) {
  fs.writeFileSync(confirmationTemplatePath, defaultConfirmationTemplate);
}

// Compile templates
const invitationTemplate = Handlebars.compile(
  fs.readFileSync(invitationTemplatePath, 'utf8')
);

const confirmationTemplate = Handlebars.compile(
  fs.readFileSync(confirmationTemplatePath, 'utf8')
);

/**
 * Send an email using AWS SES
 */
export const sendEmail = async (
  to: string,
  subject: string,
  htmlBody: string
): Promise<SendEmailCommandOutput> => {
  const params: SendEmailCommandInput = {
    Source: process.env.EMAIL_FROM || '<EMAIL>',
    Destination: {
      ToAddresses: [to],
    },
    Message: {
      Subject: {
        Data: subject,
      },
      Body: {
        Html: {
          Data: htmlBody,
        },
      },
    },
  };

  const command = new SendEmailCommand(params);
  return sesClient.send(command);
};

/**
 * Send an invitation email
 */
export const sendInvitationEmail = async (
  to: string,
  inviterName: string,
  profileName: string,
  role: string,
  invitationToken: string
): Promise<SendEmailCommandOutput> => {
  const invitationUrl = `${process.env.FRONTEND_URL}/#/invitations/accept/${invitationToken}`;

  const htmlBody = invitationTemplate({
    inviterName,
    profileName,
    role,
    invitationUrl,
  });

  return sendEmail(
    to,
    `${inviterName} has invited you to collaborate on their profile`,
    htmlBody
  );
};

/**
 * Send a confirmation email
 */
export const sendConfirmationEmail = async (
  to: string,
  profileName: string,
  role: string
): Promise<SendEmailCommandOutput> => {
  const profileUrl = `${process.env.FRONTEND_URL}/#/dashboard`;

  const htmlBody = confirmationTemplate({
    profileName,
    role,
    profileUrl,
  });

  return sendEmail(
    to,
    `You are now a collaborator on ${profileName}'s profile`,
    htmlBody
  );
};
